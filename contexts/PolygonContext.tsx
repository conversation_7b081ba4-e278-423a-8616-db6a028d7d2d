import React, { createContext, useContext, useState, ReactNode } from 'react';

// 计算多边形面积的函数（使用Shoelace公式）
const calculatePolygonArea = (coordinates: { latitude: number; longitude: number }[]) => {
  let area = 0;
  
  if (coordinates.length < 3) {
    return 0;
  }
  
  for (let i = 0; i < coordinates.length; i++) {
    const j = (i + 1) % coordinates.length;
    area += coordinates[i].latitude * coordinates[j].longitude;
    area -= coordinates[j].latitude * coordinates[i].longitude;
  }
  
  area = Math.abs(area) / 2;
  
  // 转换为公顷（粗略近似 - 在实际应用中应使用专业的地理空间库）
  // 1度纬度大约是111公里
  // 1度经度在赤道附近也大约是111公里
  const latLongToMeters = 111000; // 每度的米数
  const squareMetersToHectares = 0.0001; // 1公顷 = 10,000平方米
  
  return area * latLongToMeters * latLongToMeters * squareMetersToHectares;
};

export interface Polygon {
  id: string;
  name: string;
  coordinates: { latitude: number; longitude: number }[];
  area: number;
  color: string;
  createdAt: Date;
}

interface PolygonContextType {
  polygons: Polygon[];
  addPolygon: (coordinates: { latitude: number; longitude: number }[]) => void;
  deletePolygon: (id: string) => void;
  renamePolygon: (id: string, newName: string) => void;
  clearAllPolygons: () => void;
}

const PolygonContext = createContext<PolygonContextType | undefined>(undefined);

export const usePolygons = () => {
  const context = useContext(PolygonContext);
  if (context === undefined) {
    throw new Error('usePolygons must be used within a PolygonProvider');
  }
  return context;
};

interface PolygonProviderProps {
  children: ReactNode;
}

export const PolygonProvider: React.FC<PolygonProviderProps> = ({ children }) => {
  const [polygons, setPolygons] = useState<Polygon[]>([]);

  const addPolygon = (coordinates: { latitude: number; longitude: number }[]) => {
    const area = calculatePolygonArea(coordinates);
    const newPolygon: Polygon = {
      id: Date.now().toString(),
      name: `Polygon ${polygons.length + 1}`,
      coordinates,
      area,
      color: '#4CAF50',
      createdAt: new Date(),
    };
    setPolygons(prev => [...prev, newPolygon]);
  };

  const deletePolygon = (id: string) => {
    setPolygons(prev => prev.filter(polygon => polygon.id !== id));
  };

  const renamePolygon = (id: string, newName: string) => {
    setPolygons(prev => 
      prev.map(polygon => 
        polygon.id === id ? { ...polygon, name: newName } : polygon
      )
    );
  };

  const clearAllPolygons = () => {
    setPolygons([]);
  };

  const value: PolygonContextType = {
    polygons,
    addPolygon,
    deletePolygon,
    renamePolygon,
    clearAllPolygons,
  };

  return (
    <PolygonContext.Provider value={value}>
      {children}
    </PolygonContext.Provider>
  );
};
