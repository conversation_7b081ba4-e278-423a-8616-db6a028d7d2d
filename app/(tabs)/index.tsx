import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import { Alert, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import MapView, { Marker, Polygon, PROVIDER_GOOGLE } from 'react-native-maps';

export default function MapScreen() {
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [selectedPhoto, setSelectedPhoto] = useState<{ coordinates: { latitude: number; longitude: number }[] } | null>(null);
  const [overlayVisible, setOverlayVisible] = useState(false);
  const [polygons, setPolygons] = useState<{ latitude: number; longitude: number }[][]>([]);
  const [currentPolygon, setCurrentPolygon] = useState<{ latitude: number; longitude: number }[]>([]);
  const [isDrawingMode, setIsDrawingMode] = useState(false);

  useEffect(() => {
    (async () => {
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setErrorMsg('Permission to access location was denied');
        return;
      }

      let location = await Location.getCurrentPositionAsync({});
      setLocation(location);
    })();
  }, []);

  const handleMapPress = (event: { nativeEvent: { coordinate: { latitude: number; longitude: number } } }) => {
    if (isDrawingMode) {
      // Add point to current polygon
      const { coordinate } = event.nativeEvent;
      setCurrentPolygon([...currentPolygon, coordinate]);
    }
  };

  const completePolygon = () => {
    if (currentPolygon.length >= 3) {
      // Add current polygon to the list of polygons
      setPolygons([...polygons, currentPolygon]);
      setCurrentPolygon([]);
      setIsDrawingMode(false);
      Alert.alert('Polygon created', 'Polygon has been created successfully');
    } else {
      Alert.alert('Error', 'A polygon needs at least 3 points');
    }
  };

  const cancelPolygon = () => {
    setCurrentPolygon([]);
    setIsDrawingMode(false);
  };

  let initialRegion = {
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  };

  if (location) {
    initialRegion = {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    };
  }

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />

      {errorMsg ? (
        <Text style={styles.errorText}>{errorMsg}</Text>
      ) : (
        <View style={{ flex: 1 }}>
          <MapView
            style={styles.map}
            initialRegion={initialRegion}
            onPress={handleMapPress}
            showsUserLocation={true}
            showsMyLocationButton={true}
            mapType="satellite"
            provider={PROVIDER_GOOGLE}
          >
          {location && (
            <Marker
              coordinate={{
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
              }}
              title="Your Location"
            />
          )}

          {/* Display selected photo overlay */}
          {selectedPhoto && overlayVisible && (
            <Polygon
              coordinates={selectedPhoto.coordinates}
              fillColor="rgba(255, 0, 0, 0.2)"
              strokeColor="rgba(255, 0, 0, 0.8)"
              strokeWidth={2}
            />
          )}

          {/* Display saved polygons */}
          {polygons.map((polygon, index) => (
            <Polygon
              key={`polygon-${index}`}
              coordinates={polygon}
              fillColor="rgba(0, 255, 0, 0.2)"
              strokeColor="rgba(0, 255, 0, 0.8)"
              strokeWidth={2}
            />
          ))}

          {/* Display current polygon being drawn */}
          {currentPolygon.length > 0 && (
            <Polygon
              coordinates={currentPolygon}
              fillColor="rgba(0, 0, 255, 0.2)"
              strokeColor="rgba(0, 0, 255, 0.8)"
              strokeWidth={2}
            />
          )}
          </MapView>
        </View>
      )}

      {/* Drawing controls */}
      {isDrawingMode && (
        <View style={styles.drawingControls}>
          <TouchableOpacity style={styles.controlButton} onPress={completePolygon}>
            <MaterialCommunityIcons name="check" size={24} color="white" />
            <Text style={styles.buttonText}>Complete</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.controlButton, styles.cancelButton]} onPress={cancelPolygon}>
            <MaterialCommunityIcons name="close" size={24} color="white" />
            <Text style={styles.buttonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Floating action button */}
      {!isDrawingMode && (
        <TouchableOpacity
          style={styles.fab}
          onPress={() => setIsDrawingMode(true)}
        >
          <MaterialCommunityIcons name="vector-polygon" size={24} color="white" />
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  map: {
    width: '100%',
    height: '100%',
  },
  errorText: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    marginTop: 50,
  },
  attribution: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    padding: 2,
    borderRadius: 3,
    fontSize: 10,
    color: '#333',
  },
  fab: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    backgroundColor: '#2196F3',
    borderRadius: 28,
    width: 56,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  drawingControls: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    flexDirection: 'column',
    gap: 8,
  },
  controlButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  cancelButton: {
    backgroundColor: '#F44336',
  },
  buttonText: {
    color: 'white',
    marginLeft: 4,
    fontWeight: 'bold',
  },
});
