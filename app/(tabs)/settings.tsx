import React, { useState } from 'react';
import { StyleSheet, View, Text, Switch, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { MaterialCommunityIcons } from '@expo/vector-icons';

export default function SettingsScreen() {
  const [settings, setSettings] = useState({
    darkMode: false,
    useMetricUnits: true,
    showCoordinates: true,
    autoSavePolygons: true,
    highPrecisionGPS: true,
    showPhotoOverlay: true,
  });

  const toggleSetting = (key) => {
    setSettings({
      ...settings,
      [key]: !settings[key],
    });
  };

  const resetApp = () => {
    Alert.alert(
      'Reset App',
      'Are you sure you want to reset the app? This will delete all photos and polygons.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset', 
          style: 'destructive',
          onPress: () => {
            // In a real app, you would clear all data here
            Alert.alert('App Reset', 'All data has been cleared');
          }
        },
      ]
    );
  };

  const exportData = () => {
    // In a real app, you would implement data export functionality
    Alert.alert('Export Data', 'This feature would export all your data to a file');
  };

  const importData = () => {
    // In a real app, you would implement data import functionality
    Alert.alert('Import Data', 'This feature would import data from a file');
  };

  const renderSettingItem = (icon, title, description, value, onToggle) => (
    <View style={styles.settingItem}>
      <MaterialCommunityIcons name={icon} size={24} color="#2196F3" style={styles.settingIcon} />
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingDescription}>{description}</Text>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        trackColor={{ false: '#ccc', true: '#81c784' }}
        thumbColor={value ? '#4CAF50' : '#f4f3f4'}
      />
    </View>
  );

  const renderActionItem = (icon, title, description, onPress, color = '#2196F3') => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <MaterialCommunityIcons name={icon} size={24} color={color} style={styles.settingIcon} />
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingDescription}>{description}</Text>
      </View>
      <MaterialCommunityIcons name="chevron-right" size={24} color="#999" />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      <View style={styles.header}>
        <Text style={styles.title}>Settings</Text>
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Appearance</Text>
          {renderSettingItem(
            'theme-light-dark',
            'Dark Mode',
            'Use dark theme throughout the app',
            settings.darkMode,
            () => toggleSetting('darkMode')
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Map Settings</Text>
          {renderSettingItem(
            'ruler',
            'Use Metric Units',
            'Display measurements in hectares (or acres if disabled)',
            settings.useMetricUnits,
            () => toggleSetting('useMetricUnits')
          )}
          {renderSettingItem(
            'map-marker',
            'Show Coordinates',
            'Display GPS coordinates on the map',
            settings.showCoordinates,
            () => toggleSetting('showCoordinates')
          )}
          {renderSettingItem(
            'image',
            'Show Photo Overlay',
            'Display photo overlays on the map',
            settings.showPhotoOverlay,
            () => toggleSetting('showPhotoOverlay')
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Management</Text>
          {renderSettingItem(
            'content-save',
            'Auto-save Polygons',
            'Automatically save polygons after creation',
            settings.autoSavePolygons,
            () => toggleSetting('autoSavePolygons')
          )}
          {renderSettingItem(
            'crosshairs-gps',
            'High Precision GPS',
            'Use high precision GPS (uses more battery)',
            settings.highPrecisionGPS,
            () => toggleSetting('highPrecisionGPS')
          )}
          {renderActionItem(
            'export',
            'Export Data',
            'Export all photos and polygons',
            exportData
          )}
          {renderActionItem(
            'import',
            'Import Data',
            'Import photos and polygons from file',
            importData
          )}
          {renderActionItem(
            'delete',
            'Reset App',
            'Delete all data and reset the app',
            resetApp,
            '#F44336'
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>About</Text>
          {renderActionItem(
            'information',
            'About MapPond',
            'Version 1.0.0',
            () => Alert.alert('About', 'MapPond v1.0.0\nDeveloped with React Native and Expo')
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 16,
    paddingTop: 60,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
    marginHorizontal: 16,
    marginBottom: 8,
    marginTop: 8,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingIcon: {
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
});
