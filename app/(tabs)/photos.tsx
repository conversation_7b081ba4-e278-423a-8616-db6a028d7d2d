import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import { Alert, FlatList, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface Photo {
  id: string;
  uri: string;
  name: string;
  location?: {
    latitude: number;
    longitude: number;
    altitude?: number;
  };
  coordinates?: { latitude: number; longitude: number }[];
  selected?: boolean;
}

export default function PhotosScreen() {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    requestPermissions();
  }, []);

  const requestPermissions = async () => {
    const { status: mediaStatus } = await MediaLibrary.requestPermissionsAsync();
    const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
    const { status: mediaLibraryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (mediaStatus !== 'granted' || cameraStatus !== 'granted' || mediaLibraryStatus !== 'granted') {
      Alert.alert('Permission required', 'Please grant camera and media library permissions to use this app');
    }
  };

  const pickImage = async () => {
    try {
      setLoading(true);
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 1,
        exif: true,
      });

      if (!result.canceled) {
        const asset = result.assets[0];
        const fileInfo = await FileSystem.getInfoAsync(asset.uri);

        // Extract GPS data from EXIF if available
        let location: { latitude: number; longitude: number; altitude?: number } | null = null;
        if (asset.exif) {
          if (asset.exif.GPSLatitude && asset.exif.GPSLongitude) {
            location = {
              latitude: asset.exif.GPSLatitude,
              longitude: asset.exif.GPSLongitude,
              altitude: asset.exif.GPSAltitude || 0,
            };
          }
        }

        // Create a simple polygon around the GPS point (this is a placeholder)
        // In a real app, you would calculate this based on camera angle, altitude, etc.
        const coordinates = location ? [
          { latitude: location.latitude - 0.0005, longitude: location.longitude - 0.0005 },
          { latitude: location.latitude + 0.0005, longitude: location.longitude - 0.0005 },
          { latitude: location.latitude + 0.0005, longitude: location.longitude + 0.0005 },
          { latitude: location.latitude - 0.0005, longitude: location.longitude + 0.0005 },
        ] : [];

        const newPhoto: Photo = {
          id: Date.now().toString(),
          uri: asset.uri,
          name: asset.uri.split('/').pop() || 'photo',
          location,
          coordinates,
        };

        setPhotos([...photos, newPhoto]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    } finally {
      setLoading(false);
    }
  };

  const deletePhoto = (id: string) => {
    Alert.alert(
      'Delete Photo',
      'Are you sure you want to delete this photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setPhotos(photos.filter(photo => photo.id !== id));
          }
        },
      ]
    );
  };

  const togglePhotoSelection = (id: string) => {
    setPhotos(photos.map(photo =>
      photo.id === id ? { ...photo, selected: !photo.selected } : photo
    ));
  };

  const renderPhotoItem = ({ item }: { item: Photo }) => (
    <View style={styles.photoItem}>
      <TouchableOpacity
        style={[styles.photoContainer, item.selected && styles.selectedPhoto]}
        onPress={() => togglePhotoSelection(item.id)}
        onLongPress={() => deletePhoto(item.id)}
      >
        <Image source={{ uri: item.uri }} style={styles.photo} />
        {item.location ? (
          <View style={styles.locationBadge}>
            <MaterialCommunityIcons name="map-marker" size={12} color="white" />
          </View>
        ) : null}
      </TouchableOpacity>
      <Text style={styles.photoName} numberOfLines={1}>{item.name}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />

      <View style={styles.header}>
        <Text style={styles.title}>Photos</Text>
        <Text style={styles.subtitle}>{photos.length} photos</Text>
      </View>

      {photos.length === 0 ? (
        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons name="image-off" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No photos yet</Text>
          <Text style={styles.emptySubtext}>Tap the + button to add photos</Text>
        </View>
      ) : (
        <FlatList
          data={photos}
          renderItem={renderPhotoItem}
          keyExtractor={item => item.id}
          numColumns={3}
          contentContainerStyle={styles.photoList}
        />
      )}

      <TouchableOpacity
        style={styles.fab}
        onPress={pickImage}
        disabled={loading}
      >
        <MaterialCommunityIcons name="plus" size={24} color="white" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 16,
    paddingTop: 60,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  photoList: {
    padding: 8,
  },
  photoItem: {
    width: '33.33%',
    padding: 4,
  },
  photoContainer: {
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedPhoto: {
    borderColor: '#2196F3',
  },
  photo: {
    width: '100%',
    aspectRatio: 1,
  },
  photoName: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
  locationBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(33, 150, 243, 0.8)',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#666',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
  },
  fab: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    backgroundColor: '#2196F3',
    borderRadius: 28,
    width: 56,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});
