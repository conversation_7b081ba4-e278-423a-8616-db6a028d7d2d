import { MaterialCommunityIcons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import { Al<PERSON>, FlatList, Share, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Polygon, usePolygons } from '../../contexts/PolygonContext';

export default function PolygonsScreen() {
  const { polygons, deletePolygon: deletePolygonFromContext, renamePolygon: renamePolygonFromContext } = usePolygons();

  const handleDeletePolygon = (id: string) => {
    Alert.alert(
      'Delete Polygon',
      'Are you sure you want to delete this polygon?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            deletePolygonFromContext(id);
          }
        },
      ]
    );
  };

  const sharePolygon = async (polygon: Polygon) => {
    try {
      // Format coordinates for sharing
      const coordinatesText = polygon.coordinates
        .map(coord => `${coord.latitude.toFixed(6)},${coord.longitude.toFixed(6)}`)
        .join('\n');

      const message = `Polygon: ${polygon.name}\n\nArea: ${polygon.area.toFixed(2)} hectares\n\nCoordinates:\n${coordinatesText}`;

      await Share.share({
        message,
        title: `Polygon: ${polygon.name}`,
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to share polygon');
    }
  };

  const handleRenamePolygon = (id: string) => {
    // In a real app, you would show a dialog to enter a new name
    const newName = 'Renamed Field ' + Math.floor(Math.random() * 100);

    renamePolygonFromContext(id, newName);
  };

  const renderPolygonItem = ({ item }: { item: Polygon }) => (
    <View style={styles.polygonItem}>
      <View style={styles.polygonHeader}>
        <View style={[styles.colorIndicator, { backgroundColor: item.color }]} />
        <Text style={styles.polygonName}>{item.name}</Text>
        <Text style={styles.polygonArea}>{item.area.toFixed(2)} ha</Text>
      </View>

      <View style={styles.polygonActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleRenamePolygon(item.id)}
        >
          <MaterialCommunityIcons name="pencil" size={20} color="#2196F3" />
          <Text style={styles.actionText}>Rename</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => sharePolygon(item)}
        >
          <MaterialCommunityIcons name="share-variant" size={20} color="#4CAF50" />
          <Text style={styles.actionText}>Share</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleDeletePolygon(item.id)}
        >
          <MaterialCommunityIcons name="delete" size={20} color="#F44336" />
          <Text style={styles.actionText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />

      <View style={styles.header}>
        <Text style={styles.title}>Polygons</Text>
        <Text style={styles.subtitle}>{polygons.length} polygons</Text>
      </View>

      {polygons.length === 0 ? (
        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons name="vector-polygon" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No polygons yet</Text>
          <Text style={styles.emptySubtext}>Create polygons on the map screen</Text>
        </View>
      ) : (
        <FlatList
          data={polygons}
          renderItem={renderPolygonItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.polygonList}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 16,
    paddingTop: 60,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  polygonList: {
    padding: 16,
  },
  polygonItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 16,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  polygonHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  colorIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  polygonName: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  polygonArea: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
  },
  polygonActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  actionText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#666',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
  },
});
