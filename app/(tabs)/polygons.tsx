import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, FlatList, Alert, Share } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { MaterialCommunityIcons } from '@expo/vector-icons';

// Since we couldn't install @turf/turf due to permission issues, we'll implement a simple
// area calculation function for polygons
const calculatePolygonArea = (coordinates) => {
  // Implementation of the Shoelace formula (G<PERSON><PERSON>'s area formula)
  let area = 0;
  
  if (coordinates.length < 3) {
    return 0;
  }
  
  for (let i = 0; i < coordinates.length; i++) {
    const j = (i + 1) % coordinates.length;
    area += coordinates[i].latitude * coordinates[j].longitude;
    area -= coordinates[j].latitude * coordinates[i].longitude;
  }
  
  area = Math.abs(area) / 2;
  
  // Convert to hectares (very rough approximation - in a real app, use a proper geospatial library)
  // This is a simplification and won't be accurate for large areas due to Earth's curvature
  // 1 degree of latitude is approximately 111 km
  // 1 degree of longitude varies with latitude, but at the equator it's also about 111 km
  const latLongToMeters = 111000; // meters per degree
  const squareMetersToHectares = 0.0001; // 1 hectare = 10,000 square meters
  
  return area * latLongToMeters * latLongToMeters * squareMetersToHectares;
};

interface Polygon {
  id: string;
  name: string;
  coordinates: any[];
  area: number;
  color: string;
  createdAt: Date;
}

export default function PolygonsScreen() {
  const [polygons, setPolygons] = useState<Polygon[]>([
    // Sample polygon for demonstration
    {
      id: '1',
      name: 'Sample Field',
      coordinates: [
        { latitude: 37.78825, longitude: -122.4324 },
        { latitude: 37.78925, longitude: -122.4344 },
        { latitude: 37.78995, longitude: -122.4324 },
        { latitude: 37.78895, longitude: -122.4304 },
      ],
      area: 1.25,
      color: '#4CAF50',
      createdAt: new Date(),
    },
  ]);

  const deletePolygon = (id: string) => {
    Alert.alert(
      'Delete Polygon',
      'Are you sure you want to delete this polygon?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => {
            setPolygons(polygons.filter(polygon => polygon.id !== id));
          }
        },
      ]
    );
  };

  const sharePolygon = async (polygon: Polygon) => {
    try {
      // Format coordinates for sharing
      const coordinatesText = polygon.coordinates
        .map(coord => `${coord.latitude.toFixed(6)},${coord.longitude.toFixed(6)}`)
        .join('\n');
      
      const message = `Polygon: ${polygon.name}\n\nArea: ${polygon.area.toFixed(2)} hectares\n\nCoordinates:\n${coordinatesText}`;
      
      await Share.share({
        message,
        title: `Polygon: ${polygon.name}`,
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to share polygon');
    }
  };

  const renamePolygon = (id: string) => {
    // In a real app, you would show a dialog to enter a new name
    const newName = 'Renamed Field ' + Math.floor(Math.random() * 100);
    
    setPolygons(polygons.map(polygon => 
      polygon.id === id ? { ...polygon, name: newName } : polygon
    ));
  };

  const renderPolygonItem = ({ item }: { item: Polygon }) => (
    <View style={styles.polygonItem}>
      <View style={styles.polygonHeader}>
        <View style={[styles.colorIndicator, { backgroundColor: item.color }]} />
        <Text style={styles.polygonName}>{item.name}</Text>
        <Text style={styles.polygonArea}>{item.area.toFixed(2)} ha</Text>
      </View>
      
      <View style={styles.polygonActions}>
        <TouchableOpacity 
          style={styles.actionButton} 
          onPress={() => renamePolygon(item.id)}
        >
          <MaterialCommunityIcons name="pencil" size={20} color="#2196F3" />
          <Text style={styles.actionText}>Rename</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton} 
          onPress={() => sharePolygon(item)}
        >
          <MaterialCommunityIcons name="share-variant" size={20} color="#4CAF50" />
          <Text style={styles.actionText}>Share</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton} 
          onPress={() => deletePolygon(item.id)}
        >
          <MaterialCommunityIcons name="delete" size={20} color="#F44336" />
          <Text style={styles.actionText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      <View style={styles.header}>
        <Text style={styles.title}>Polygons</Text>
        <Text style={styles.subtitle}>{polygons.length} polygons</Text>
      </View>

      {polygons.length === 0 ? (
        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons name="vector-polygon-off" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No polygons yet</Text>
          <Text style={styles.emptySubtext}>Create polygons on the map screen</Text>
        </View>
      ) : (
        <FlatList
          data={polygons}
          renderItem={renderPolygonItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.polygonList}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 16,
    paddingTop: 60,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  polygonList: {
    padding: 16,
  },
  polygonItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 16,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  polygonHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  colorIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  polygonName: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  polygonArea: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
  },
  polygonActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  actionText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#666',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
  },
});
